/**
 * Settings Controller
 * Handles HTTP requests related to system settings
 */
const { Setting } = require('../models');
const { AppError, <PERSON>ting<PERSON><PERSON><PERSON><PERSON>, Response<PERSON>elper, logError, logSuccess, logInfo } = require('../utils');

/**
 * SettingsController handles settings-related HTTP requests
 */
const SettingsController = {
  /**
   * Get all available payment methods
   * GET /api/settings/payment-methods
   */
  getPaymentMethods: async (req, res, next) => {
    try {
      // Get context if provided (default to general)
      const { context = 'general' } = req.query;
      
      // Get methods based on context and settings
      let paymentMethods = [];
      let isOnlineEnabled = true;
      let isOfflineEnabled = true;
      
      // Get global switches
      const onlineSetting = await Setting.findOne({
        key: 'enable_online_payment',
        shopId: null
      });
      
      const offlineSetting = await Setting.findOne({
        key: 'enable_offline_payment',
        shopId: null
      });
      
      if (onlineSetting) {
        isOnlineEnabled = onlineSetting.value;
      }
      
      if (offlineSetting) {
        isOfflineEnabled = offlineSetting.value;
      }
      
      // Get context-specific settings
      let settingKey = 'payment_methods_available';
      if (context === 'subscription') {
        settingKey = 'subscription_payment_methods';
      }
      
      // Get methods from settings
      const methodsSetting = await Setting.findOne({
        key: settingKey,
        shopId: null
      });
      
      if (methodsSetting) {
        // Filter methods based on global switches
        const methods = methodsSetting.value || [];
        
        paymentMethods = methods.filter(method => {
          const isOnlineMethod = ['EVC Plus', 'Card', 'Mobile Money'].includes(method);
          const isOfflineMethod = ['Cash', 'Bank Transfer', 'Check', 'Other', 'offline'].includes(method);
          
          return (isOnlineMethod && isOnlineEnabled) || 
                 (isOfflineMethod && isOfflineEnabled) || 
                 (!isOnlineMethod && !isOfflineMethod);
        });
      } else {
        // Default methods if no setting found
        const defaultMethods = ['Cash', 'EVC Plus', 'Bank Transfer', 'offline'];
        
        paymentMethods = defaultMethods.filter(method => {
          const isOnlineMethod = ['EVC Plus', 'Card', 'Mobile Money'].includes(method);
          const isOfflineMethod = ['Cash', 'Bank Transfer', 'Check', 'Other', 'offline'].includes(method);
          
          return (isOnlineMethod && isOnlineEnabled) || 
                 (isOfflineMethod && isOfflineEnabled) || 
                 (!isOnlineMethod && !isOfflineMethod);
        });
      }
      
      return res.status(200).json({
        success: true,
        data: {
          paymentMethods,
          onlinePaymentsEnabled: isOnlineEnabled,
          offlinePaymentsEnabled: isOfflineEnabled,
          context
        }
      });
    } catch (error) {
      logError(`Error getting payment methods: ${error.message}`, 'SettingsController', error);
      return next(error);
    }
  },
  
  /**
   * Get current system settings
   * GET /api/settings
   * Requires admin authentication
   */
  getSettings: async (req, res, next) => {
    try {
      // Get category from query
      const { category = 'all' } = req.query;
      
      let query = { shopId: null }; // Global settings only
      
      // Filter by category if provided
      if (category !== 'all') {
        query.category = category;
      }
      
      // Get settings (exclude sensitive data)
      const settings = await Setting.find(query).select('-history -__v');
      
      // Format settings for response
      const formattedSettings = settings.map(setting => ({
        key: setting.key,
        category: setting.category,
        displayName: setting.displayName,
        description: setting.description,
        value: setting.value,
        dataType: setting.dataType,
        isEditable: setting.isEditable,
        accessLevel: setting.accessLevel
      }));
      
      return res.status(200).json({
        success: true,
        data: formattedSettings
      });
    } catch (error) {
      logError(`Error getting settings: ${error.message}`, 'SettingsController', error);
      return next(error);
    }
  },
  
  /**
   * Get EVC payment credentials
   * GET /api/settings/evc-credentials
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  getEVCCredentials: async (req, res, next) => {
    try {
      const { shopId } = req.query;
      
      // Authorize based on role and shop access
      // Shop admins can only view their own shop's credentials
      if (req.user?.role === 'admin') {
        // For admin users, use their assigned shopId if no shopId provided
        const targetShopId = shopId || req.user?.shopId;
        
        if (!targetShopId) {
          throw new AppError('Shop ID is required for shop admin credential access', 400);
        }
        
        // Check if admin has access to this shop
        if (req.user?.shopId !== targetShopId) {
          throw new AppError('You do not have permission to view credentials for this shop', 403);
        }
      }
      
      // Only superAdmins can view global credentials
      if (!shopId && req.user?.role !== 'superAdmin') {
        throw new AppError('Only super administrators can view global payment credentials', 403);
      }
      
      // Import required service
      const { SettingsHelper } = require('../utils');
      
      // Get credentials using SettingsHelper (handles decryption)
      const credentials = await SettingsHelper.getEVCCredentials(shopId);
      
      if (!credentials || Object.keys(credentials).length === 0) {
        return ResponseHelper.success(res, {
          success: true,
          message: `No EVC credentials found for ${shopId ? 'shop ' + shopId : 'global system'}`,
          data: {}
        });
      }
      
      // Return credentials (sensitive fields are already masked by SettingsHelper)
      logInfo(`EVC credentials retrieved for ${shopId ? 'shop ' + shopId : 'global system'}`, 'SettingsController');
      
      return ResponseHelper.success(res, {
        success: true,
        message: `EVC credentials retrieved for ${shopId ? 'shop ' + shopId : 'global system'}`,
        data: credentials
      });
    } catch (error) {
      logError(`Failed to get EVC credentials: ${error.message}`, 'SettingsController', error);
      return next(error);
    }
  },
  
  /**
   * Set EVC payment credentials (encrypted in database)
   * POST /api/settings/evc-credentials
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  setEVCCredentials: async (req, res, next) => {
    console.log('[EVC DEBUG] Received payload:', req.body);
    try {
      const { merchantUId, apiUserId, apiKey, merchantNo, url, shopId } = req.body;
      
      // Basic validation
      if (!merchantUId || !apiUserId || !apiKey || !merchantNo) {
        throw new AppError('Missing required EVC payment credentials', 400);
      }
      
      // Authorize based on role and shop access
      // Shop admins can only manage their own shop's credentials
      if (req.user?.role === 'admin') {
        // Ensure shopId is provided and matches the admin's shop
        if (!shopId) {
          throw new AppError('Shop ID is required for shop admin credential management', 400);
        }
        
        // Check if admin has access to this shop
        if (req.user?.shopId !== shopId) {
          throw new AppError('You do not have permission to manage credentials for this shop', 403);
        }
      }
      
      // Only superAdmins can set global credentials
      if (!shopId && req.user?.role !== 'superAdmin') {
        throw new AppError('Only super administrators can manage global payment credentials', 403);
      }
      
      // Import required service
      const { SettingsHelper } = require('../utils');
      
      // Pass plain credentials to SettingsHelper which will handle encryption
      // with shop-specific key for cryptographic isolation
      await SettingsHelper.createOrUpdateEVCCredentials(
        {
          merchantUId,
          apiUserId,
          apiKey,
          merchantNo,
          url: url || 'https://api.waafipay.net/asm'
        },
        shopId,
        req.user?.email || req.user?.userId || 'system',
        false // Not encrypted yet - let SettingsHelper handle it
      );
      
      return ResponseHelper.success(res, {
        message: `EVC payment credentials stored securely for ${shopId ? 'shop ' + shopId : 'global system'}`
      });
    } catch (error) {
      logError(`Failed to set EVC credentials: ${error.message}`, 'SettingsController', error);
      return next(error);
    }
  },
  
  /**
   * Test EVC payment credentials
   * POST /api/settings/test-evc-credentials
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  testEVCCredentials: async (req, res, next) => {
    try {
      const { shopId, phone, amount } = req.body;
      
      // Check if this is a real payment test or just credential validation
      const isRealPaymentTest = phone && amount;
      
      // Add diagnostic information to logs
      if (isRealPaymentTest) {
        logInfo(`Real EVC payment test requested for phone: ${phone}, amount: ${amount}`, 'SettingsController');
      } else {
        logInfo(`EVC credential validation test requested for ${shopId ? 'shop: ' + shopId : 'global system'}`, 'SettingsController');
      }
      
      // Authorize based on role and shop access
      // Shop admins can only test their own shop's credentials
      if (req.user?.role === 'admin') {
        // Ensure shopId is provided and matches the admin's shop
        if (!shopId) {
          throw new AppError('Shop ID is required for shop admin credential testing', 400);
        }
        
        // Check if admin has access to this shop
        if (req.user?.shopId !== shopId) {
          throw new AppError('You do not have permission to test credentials for this shop', 403);
        }
      }
      
      // Import required services
      const EVCPaymentService = require('../services/evcPaymentService');
      const { SettingsHelper } = require('../utils');
      
      // Privacy protection: SuperAdmins can only check if credentials exist, not view or test them
      // unless they are testing global credentials
      if (shopId && req.user?.role === 'superAdmin' && !isRealPaymentTest) {
        // SuperAdmin can only check if shop credentials exist, not test actual values
        const credentialStatus = await SettingsHelper.hasEVCCredentials(shopId);
        
        if (!credentialStatus.hasCredentials) {
          return ResponseHelper.success(res, {
            success: false,
            message: `Shop ${shopId} does not have EVC credentials configured`
          });
        }
        
        return ResponseHelper.success(res, {
          success: true,
          message: `Shop ${shopId} has EVC credentials configured (last updated: ${credentialStatus.lastUpdated})`,
          hasCredentials: true,
          lastUpdated: credentialStatus.lastUpdated
        });
      }
      
      let result;
      
      if (isRealPaymentTest) {
        // Perform a real test payment with minimal amount
        result = await EVCPaymentService.testPayment({
          phone,
          amount: parseFloat(amount) || 0.1, // Default to $0.5 if amount parsing fails
          shopId,
          description: 'DeynCare EVC Integration Test',
          reference: `test-${Date.now()}`
        });
      } else {
        // Just test connectivity with credentials validation
        result = await EVCPaymentService.testConnection(shopId);
      }
      
      return ResponseHelper.success(res, {
        success: result.success,
        message: result.message,
        transactionId: result.transactionId || null
      });
    } catch (error) {
      logError(`Failed to test EVC credentials: ${error.message}`, 'SettingsController', error);
      return ResponseHelper.error(res, {
        message: `Failed to test EVC credentials: ${error.message}`,
        success: false
      }, 500);
    }
  },
  
  /**
   * Update payment method settings
   * PUT /api/settings/payment-methods
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  updatePaymentMethods: async (req, res, next) => {
    try {
      const { enableOnline, enableOffline, context, methods, shopId } = req.body;
      
      // Validate user has permissions
      if (!req.user || !['admin', 'superAdmin'].includes(req.user.role)) {
        throw new AppError('Only admin users can update payment methods', 403, 'settings_permission_denied');
      }
      
      // Add diagnostic header to prevent frontend misinterpreting this as auth issue
      res.set('X-Error-Context', 'settings_update');
      
      // Import required services
      const { SettingsHelper } = require('../utils');
      
      // Update global online/offline switches
      if (typeof enableOnline === 'boolean') {
        await SettingsHelper.updateSetting(
          'enable_online_payment',
          null, // shopId for global setting
          enableOnline,
          req.user?.email || req.user?.userId || 'system',
          req.user?.role, // Pass userRole
          'Manual update via API'
        );
      }
      
      if (typeof enableOffline === 'boolean') {
        await SettingsHelper.updateSetting(
          'enable_offline_payment',
          null, // shopId for global setting
          enableOffline,
          req.user?.email || req.user?.userId || 'system',
          req.user?.role, // Pass userRole
          'Manual update via API'
        );
      }
      
      // Update context-specific methods if provided
      if (context && methods && Array.isArray(methods)) {
        let settingKey = 'payment_methods_available';
        
        if (context === 'subscription') {
          settingKey = 'subscription_payment_methods';
        }
        
        await SettingsHelper.updateSetting(
          settingKey,
          shopId, // Fixed: shopId parameter
          methods, // Fixed: value parameter (methods array)
          req.user?.email || req.user?.userId || 'system', // Fixed: updatedBy parameter
          req.user?.role, // Fixed: userRole parameter
          `Updated ${context} payment methods` // Fixed: reason parameter
        );
      }
      
      return ResponseHelper.success(res, {
        message: 'Payment methods updated successfully',
        context,
        shopId: shopId || 'global'
      });
    } catch (error) {
      logError(`Failed to update payment methods: ${error.message}`, 'SettingsController', error);
      return next(error);
    }
  },

  /**
   * Update security settings
   * PUT /api/settings
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  updateSecuritySettings: async (req, res, next) => {
    try {
      // Only SuperAdmin can update security settings
      if (req.user.role !== 'superAdmin') {
        throw new AppError('Only SuperAdmin can update security settings', 403, 'settings_permission_denied'); // Added error code
      }
      
      // Add diagnostic headers to prevent frontend misinterpreting this as auth issue
      res.set('X-Error-Context', 'settings_update');
      res.set('X-Auth-Status', 'valid');
      
      // Get security settings from request body
      const { passwordPolicy, sessionSettings, securityHeaders } = req.body;
      
      // Update password policy settings
      if (passwordPolicy) {
        const passwordPolicyUpdates = [
          { key: 'password.minLength', value: passwordPolicy.minLength || 8 },
          { key: 'password.requireUppercase', value: passwordPolicy.requireUppercase !== false },
          { key: 'password.requireLowercase', value: passwordPolicy.requireLowercase !== false },
          { key: 'password.requireNumbers', value: passwordPolicy.requireNumbers !== false },
          { key: 'password.requireSpecialChars', value: passwordPolicy.requireSpecialChars !== false },
          { key: 'password.passwordExpiryDays', value: passwordPolicy.passwordExpiryDays || 90 }
        ];
        
        // Update each setting
        for (const update of passwordPolicyUpdates) {
          await Setting.findOneAndUpdate(
            { key: update.key, category: 'security', shopId: null },
            { 
              $set: { value: update.value },
              $setOnInsert: { 
                category: 'security',
                displayName: update.key.split('.')[1].charAt(0).toUpperCase() + update.key.split('.')[1].slice(1),
                description: `Password policy setting for ${update.key.split('.')[1]}`,
                dataType: typeof update.value,
                isEditable: true,
                accessLevel: 'superAdmin'
              }
            },
            { upsert: true, new: true }
          );
        }
      }
      
      // Update session settings
      if (sessionSettings) {
        const sessionUpdates = [
          { key: 'session.timeout', value: sessionSettings.sessionTimeout || 30 },
          { key: 'login.maxAttempts', value: sessionSettings.maxLoginAttempts || 5 },
          { key: 'login.lockoutDuration', value: sessionSettings.lockoutDuration || 15 }
        ];
        
        // Update each setting
        for (const update of sessionUpdates) {
          await Setting.findOneAndUpdate(
            { key: update.key, category: 'security', shopId: null },
            { 
              $set: { value: update.value },
              $setOnInsert: { 
                category: 'security',
                displayName: update.key.includes('timeout') ? 'Session Timeout' : 
                            update.key.includes('maxAttempts') ? 'Max Login Attempts' : 'Lockout Duration',
                description: update.key.includes('timeout') ? 'Auto-logout inactive users after this many minutes' : 
                            update.key.includes('maxAttempts') ? 'Maximum failed login attempts before lockout' : 
                            'Account lockout duration in minutes after failed attempts',
                dataType: 'number',
                isEditable: true,
                accessLevel: 'superAdmin'
              }
            },
            { upsert: true, new: true }
          );
        }
      }
      
      // Update security header settings
      if (securityHeaders) {
        const headerUpdates = [
          { key: 'headers.enableCSP', value: securityHeaders.enableCSP !== false },
          { key: 'headers.enableHSTS', value: securityHeaders.enableHSTS !== false },
          { key: 'headers.enableXFrameOptions', value: securityHeaders.enableXFrameOptions !== false },
          { key: 'headers.enableXSSProtection', value: securityHeaders.enableXSSProtection !== false }
        ];
        
        // Update each setting
        for (const update of headerUpdates) {
          await Setting.findOneAndUpdate(
            { key: update.key, category: 'security', shopId: null },
            { 
              $set: { value: update.value },
              $setOnInsert: { 
                category: 'security',
                displayName: update.key.split('.')[1].replace('enable', '').replace(/([A-Z])/g, ' $1').trim(),
                description: `Enable ${update.key.split('.')[1].replace('enable', '')} security header`,
                dataType: 'boolean',
                isEditable: true,
                accessLevel: 'superAdmin'
              }
            },
            { upsert: true, new: true }
          );
        }
      }
      
      // Log success
      logSuccess('Security settings updated successfully', 'SettingsController');
      
      return res.status(200).json({
        success: true,
        message: 'Security settings updated successfully'
      });
    } catch (error) {
      logError(`Error updating security settings: ${error.message}`, 'SettingsController', error);
      return next(error);
    }
  },

  /**
   * Update a specific setting by key
   * PATCH /api/settings/:key
   * Requires authentication. Admin can update settings with accessLevel 'admin' for their shop.
   * SuperAdmins can update any setting.
   */
  updateSettingByKey: async (req, res, next) => {
    try {
      const { key } = req.params; // Get the setting key from the URL
      const { value, shopId: requestedShopId } = req.body; // Get the new value and optional shopId from body
      const user = req.user; // Get user info from auth middleware

      // Basic validation (more detailed validation is in schema/model)
      if (value === undefined) {
        throw new AppError('Setting value is required in the request body', 400, 'missing_setting_value');
      }

      // Determine the effective shopId for the update
      // Admin users can only update settings for their assigned shop
      // SuperAdmins can update global or specific shop settings (if requestedShopId is provided)
      let effectiveShopId = null; // Default to global

      if (user.role === 'admin') {
        // Admins are restricted to their assigned shop
        if (!user.shopId) {
          // This case should ideally be prevented earlier or indicate misconfiguration
          throw new AppError('Admin user not assigned to a shop', 403, 'admin_shop_assignment_missing');
        }
        effectiveShopId = user.shopId;

        // If the Admin tries to provide a different shopId in the body, reject it
        if (requestedShopId !== undefined && requestedShopId !== null && requestedShopId !== effectiveShopId) {
             throw new AppError('Admin users can only update settings for their assigned shop', 403, 'unauthorized_shop_setting');
        }

      } else if (user.role === 'superAdmin') {
         // SuperAdmins can specify a shopId in the body to update a shop-specific setting
         // If no shopId is provided in the body, it defaults to updating the global setting (shopId: null)
         effectiveShopId = requestedShopId === undefined || requestedShopId === null ? null : requestedShopId;
      } else {
          // Should not happen if authorize middleware works, but as a safeguard
          throw new AppError('Unauthorized role', 403, 'unauthorized_role');
      }

      // Import the SettingsHelper
      const { SettingsHelper } = require('../utils');

      // Call a helper/service function to perform the update with authorization checks
      // We'll implement/verify updateSetting function in SettingsHelper next
      const updatedSetting = await SettingsHelper.updateSetting(
        key,
        effectiveShopId,
        value,
        user.userId, // Pass the user who is making the update for the audit trail
        user.role // Pass the user role for authorization checks in the helper
      );

      return res.status(200).json({
        success: true,
        message: `Setting '${key}' for ${effectiveShopId ? 'shop ' + effectiveShopId : 'global system'} updated successfully`,
        data: updatedSetting // Return the updated setting document
      });

    } catch (error) {
      logError(`Failed to update setting by key '${req.params.key}'`, 'SettingsController', error);
      return next(error);
    }
  }
};

module.exports = SettingsController;
